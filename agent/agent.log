2025-06-09 13:23:22,785 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-09 13:23:22,789 - livekit.agents - INFO - starting worker
2025-06-09 13:23:22,791 - livekit.agents - INFO - [1msee tracing information at http://localhost:54702/debug[0m
2025-06-09 13:23:22,792 - livekit.agents - WARNING - failed to connect to livekit, retrying in 0s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:22,793 - livekit.agents - WARNING - failed to connect to livekit, retrying in 2s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:24,795 - livekit.agents - WARNING - failed to connect to livekit, retrying in 4s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:28,882 - livekit.agents - WARNING - failed to connect to livekit, retrying in 6s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:34,917 - livekit.agents - WARNING - failed to connect to livekit, retrying in 8s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:42,971 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:52,978 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:02,984 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:12,990 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:22,993 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:32,996 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:42,999 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:53,001 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:03,006 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:13,099 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:27,208 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:32,477 - livekit.agents - WARNING - Running <Task pending name='worker_conn_task' coro=<Worker._connection_task() running at /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py:16> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /Users/<USER>/miniconda3/lib/python3.13/asyncio/tasks.py:820]> took too long: 4.28 seconds
2025-06-09 13:25:42,080 - livekit.agents - ERROR - Error in _connection_task
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:25:43,061 - livekit.agents - ERROR - worker failed
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/cli/_run.py", line 88, in _worker_run
    await worker.run()
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 483, in run
    await asyncio.gather(*tasks)
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:25:45,463 - livekit.agents - WARNING - Running <Task finished name='agent_runner' coro=<run_worker.<locals>._worker_run() done, defined at /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/cli/_run.py:86> result=None> took too long: 2.40 seconds
2025-06-09 13:25:45,869 - livekit.agents - INFO - shutting down worker
2025-06-09 13:54:16,808 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-09 13:54:16,809 - livekit.agents - DEV - Watching /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent
2025-06-09 13:54:17,342 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-09 13:54:17,346 - livekit.agents - INFO - starting worker
2025-06-09 13:54:17,349 - livekit.agents - INFO - [1msee tracing information at http://localhost:57531/debug[0m
2025-06-09 13:54:17,349 - livekit.agents - WARNING - failed to connect to livekit, retrying in 0s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:17,351 - livekit.agents - WARNING - failed to connect to livekit, retrying in 2s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:19,353 - livekit.agents - WARNING - failed to connect to livekit, retrying in 4s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:23,354 - livekit.agents - WARNING - failed to connect to livekit, retrying in 6s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:29,357 - livekit.agents - WARNING - failed to connect to livekit, retrying in 8s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:37,359 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:47,363 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:57,367 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:07,372 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:17,376 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:27,381 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:37,385 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:47,388 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:57,392 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:56:07,394 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:56:17,397 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:56:27,401 - livekit.agents - ERROR - Error in _connection_task
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:56:27,402 - livekit.agents - ERROR - worker failed
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/cli/_run.py", line 88, in _worker_run
    await worker.run()
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 483, in run
    await asyncio.gather(*tasks)
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:56:27,404 - livekit.agents - INFO - shutting down worker
